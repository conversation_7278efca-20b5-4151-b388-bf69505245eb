import { HttpClient, HttpResponse } from '@angular/common/http'; // Add HttpClient import
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { take } from 'rxjs/operators';
import { AgencyService } from 'src/app/shared/services/agency.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CurrencyService } from 'src/app/shared/services/currency.service';
import { InventoryService } from 'src/app/shared/services/inventory.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { TransportService } from 'src/app/shared/services/transport.service';
import { CompressImageService } from 'src/app/shared/utilities/compress-image';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-vehicle-inventory-tab',
  templateUrl: './vehicle-inventory-tab.component.html',
  styleUrls: ['./vehicle-inventory-tab.component.css'],
  standalone: false,
})
export class VehicleInventoryTabComponent implements OnChanges {
  @Input('form') form: UntypedFormGroup;
  @Input('job') job: any;
  @Input('tabindex') tabindex: string;
  @Output() formO = new EventEmitter<UntypedFormGroup>();
  @Output() tabindexO = new EventEmitter<string>();

  directorates: any;

  page = 1;
  pageSize = 20;
  totalItems = 0;
  showin = 0;
  first: boolean;
  last: boolean;

  userType: string;
  clients: any;
  searchCriteria: string = null;
  isModalOpen = false;
  items = [];
  selectedItem = '';
  dateInstalled;
  description;
  isAddon = false;
  price;
  nextCheckDate;
  Actions;
  selectedFile: File = null;
  inventoryPics = [];
  currency = environment.currency; // Default fallback
  agency: any;
  agencyId: string;

  constructor(
    private toast: ToastrService,
    private transporService: TransportService,
    private storageService: StorageService,
    private agencyService: AgencyService,
    private currencyService: CurrencyService,
    private fb: UntypedFormBuilder,
    private router: Router,
    private modalService: NgbModal,
    private inventoryService: InventoryService,
    private compressImage: CompressImageService,
    private authService: AuthService,
    private http: HttpClient // Inject HttpClient here
  ) {
    this.userType = authService.getUserType();
    this.loadAgencyInfo();
  }

  ngOnChanges(): void {
    console.log(this.job);
    this.inventoryService.currentInventory.subscribe((inventory) => {
      if (inventory) {
        this.selectedItem = inventory.item;
      }
    });
  }

  get filteredInventory() {
    return this.job.inventory.filter(item => !item.price);
  }
  get filteredAddons() {
    return this.job.inventory.filter(item => item.price);
  }

  onChange(event) {
    console.log(event.target.files);

    for (let i = 0; i < event.target.files.length; i++) {
      let image = event.target.files[i];

      console.log(`Image size before compressed: ${image.size} bytes.`);
      this.compressImage
        .compress(image)
        .pipe(take(1))
        .subscribe((compressedImage) => {
          console.log(
            `Image size after compressed: ${compressedImage.size} bytes.`
          );
          this.inventoryPics.push(compressedImage);
        });
    }

    console.log(this.inventoryPics);
  }

  submitInventories() {

    if(this.selectedItem==null)  return this.toast.warning("Enter inventory item name");
    if(this.nextCheckDate==null && !this.isAddon)  return this.toast.warning("Enter next check date");
    if(this.dateInstalled==null && !this.isAddon)  return this.toast.warning("Enter date installed");
    if(this.price==null && this.isAddon)  return this.toast.warning("Enter daily rate");

        let data = {
          'vehicle': {'id':  this.job.id},
          'vehicleId': this.job.id,
          'name': this.selectedItem,
          'dateInstalled': this.dateInstalled,
          'nextCheckDate': this.nextCheckDate,
          'price': this.price,
          'description': this.description,
        }

    this.transporService
      .saveInventoryUrl(
        data
      )
      .subscribe(
        (response: HttpResponse<any>) => { // Directly handle the response
          this.toast.info('Successfully uploaded.');
          this.modalService.dismissAll();
          this.tabindexO.emit(this.tabindex);
        },
        error => {
          this.toast.error('Failed to upload inventory.');
        }
      );
  }


  openModalFunction(content: any, isAddon) {
    this.price = null;
    this.description = null;
    this.dateInstalled = null;
    this.nextCheckDate = null;
    this.selectedItem = null;

    this.isAddon = isAddon;
    this.items = isAddon ?
    [
      'Cooler box',
      'Trailer',
      'Canopy',
      'Camping chairs',
      'Chauffeur',
      'Jerry can',
      'Baby Seat'
    ]:
    [
      'High Visibility Vest',
      'Fire Extinguisher',
      'Spanners',
      'Spare Wheel',
      'Mats',
      'Jumpers',
      'Warning Triangle',
      'Jack',
    ];
    this.modalService.open(content, { centered: true, size: 'sm' });
  }

  closeModalFunction() {
    this.modalService.dismissAll();
  }

  addItem() {
    const newItem = {
      item: 'New Item',
      date: new Date(),
      images: [],
    };
    this.inventoryService.updateInventory(newItem);
  }

  deleteInventoryItem(id: any): void {
    if (id) {
      this.inventoryService.deleteInventoryItem(id).subscribe(
        resp => {
          this.toast.success('Inventory Item Deleted Successfully');
          this.updateInventoryList();
          this.modalService.dismissAll();
          this.formO.emit(this.form);
          this.tabindexO.emit(this.tabindex);
        },
      );
    }
  }

  updateInventory(inventory: any) {
    this.inventoryService.updateInventory(inventory);
  }

  private updateInventoryList() {
    // Implement the logic to refresh the inventory list
  }

  handlePageChange(event: string) {
    if (event === 'next') {
      this.page += 1;
    } else if (event === 'prev' && this.page !== 1) {
      this.page -= 1;
    }
  }

  nextTab() {
    console.log(this.tabindex);
    this.tabindex = 'logs';
    this.tabindexO.emit(this.tabindex);
    this.formO.emit(this.form);
  }

  prevTab() {
    console.log(this.tabindex);
    this.tabindex = 'docs';
    this.tabindexO.emit(this.tabindex);
    this.formO.emit(this.form);
  }

  updateCleaningCheck() {
    this.transporService
      .updateCleaningCheck(this.form.value)
      .subscribe((data) => {
        this.toast.success('Info updated');
      });
  }

  goBack() {
    this.prevTab();
  }

  goNext() {
    this.nextTab();
  }

  onFileSelected(event) {
    this.selectedFile = <File>event.target.files[0];
  }

  onUpload() {
    const fd = new FormData();
    fd.append('image', this.selectedFile, this.selectedFile.name);
    this.http
      .post(
        'https://angularbyprocademy-dbe68-default-rtdb.firebaseio.com/products.json',
        fd
      )
      .subscribe((res) => {
        console.log(res);
      });
  }

  openModal() {
    this.isModalOpen = true;
  }

  closeModal() {
    this.isModalOpen = false;
  }

  loadAgencyInfo(): void {
    this.agencyId = this.storageService.decrypt(localStorage.getItem('agentId'));
    if (this.agencyId) {
      this.agencyService.getAgencyById(this.agencyId).subscribe(
        data => {
          this.agency = data;
          this.updateCurrencySymbol();
        },
        err => {
          console.error('Failed to load agency info:', err);
          // Keep default currency symbol
        }
      );
    }
  }

  updateCurrencySymbol(): void {
    const baseCurrency = this.agency?.baseCurrency || 'USD';
    try {
      this.currency = this.currencyService.getCurrencySymbol(baseCurrency);
    } catch (err) {
      console.error('Failed to get currency symbol:', err);
      // Fallback to basic symbol mapping
      this.currency = this.getBasicCurrencySymbol(baseCurrency);
    }
  }

  getBasicCurrencySymbol(currencyCode: string): string {
    const symbolMap: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'CAD': 'C$',
      'AUD': 'A$',
      'JPY': '¥',
      'CHF': 'CHF',
      'SEK': 'kr',
      'NOK': 'kr',
      'DKK': 'kr',
      'PLN': 'zł'
    };
    return symbolMap[currencyCode] || currencyCode;
  }
}
