"use client";

import { useReducer, useEffect, useCallback, useMemo } from "react";
import { Currency } from "@/common/models";
import { CurrencyService } from "@/common/services/currency.service";

interface SupportedCurrenciesState {
  currencies: Currency[];
  currencyCodes: string[];
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
}

type SupportedCurrenciesAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CURRENCIES'; payload: { currencies: Currency[]; lastFetched: number } }
  | { type: 'SET_FROM_CACHE'; payload: SupportedCurrenciesState };

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const CACHE_KEY = "supported_currencies_cache";

function supportedCurrenciesReducer(
  state: SupportedCurrenciesState,
  action: SupportedCurrenciesAction
): SupportedCurrenciesState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload, error: null };
    case 'SET_ERROR':
      return { ...state, isLoading: false, error: action.payload };
    case 'SET_CURRENCIES':
      return {
        ...state,
        currencies: action.payload.currencies,
        currencyCodes: action.payload.currencies.map(c => c.code),
        isLoading: false,
        error: null,
        lastFetched: action.payload.lastFetched,
      };
    case 'SET_FROM_CACHE':
      return action.payload;
    default:
      return state;
  }
}

/**
 * Hook for managing supported currencies with caching
 */
export function useSupportedCurrencies() {
  const [state, dispatch] = useReducer(supportedCurrenciesReducer, {
    currencies: [],
    currencyCodes: [],
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const currencyService = useMemo(() => new CurrencyService(), []);

  /**
   * Load cached data from localStorage
   */
  const loadFromCache = useCallback((): SupportedCurrenciesState | null => {
    if (typeof window === "undefined") return null;

    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return null;

      const data = JSON.parse(cached) as SupportedCurrenciesState;
      const now = Date.now();

      // Check if cache is still valid
      if (data.lastFetched && (now - data.lastFetched) < CACHE_DURATION) {
        return data;
      }
    } catch (error) {
      console.warn("Failed to load currencies from cache:", error);
    }

    return null;
  }, []);

  /**
   * Save data to cache
   */
  const saveToCache = useCallback((data: SupportedCurrenciesState) => {
    if (typeof window === "undefined") return;

    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn("Failed to save currencies to cache:", error);
    }
  }, []);

  /**
   * Fetch supported currencies from API
   */
  const fetchCurrencies = useCallback(async (force = false) => {
    // Check cache first unless forced
    if (!force) {
      const cached = loadFromCache();
      if (cached) {
        dispatch({ type: 'SET_FROM_CACHE', payload: cached });
        return cached.currencies;
      }
    }

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const currencies = await currencyService.getActiveCurrencies();
      const now = Date.now();

      dispatch({
        type: 'SET_CURRENCIES',
        payload: { currencies, lastFetched: now }
      });

      const newState: SupportedCurrenciesState = {
        currencies,
        currencyCodes: currencies.map(c => c.code),
        isLoading: false,
        error: null,
        lastFetched: now,
      };
      saveToCache(newState);

      return currencies;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch currencies";
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, [currencyService, loadFromCache, saveToCache]);

  /**
   * Check if a currency is supported
   */
  const isCurrencySupported = useCallback((currencyCode: string): boolean => {
    return state.currencyCodes.includes(currencyCode);
  }, [state.currencyCodes]);

  /**
   * Get filtered currencies based on common currencies
   */
  const getFilteredCurrencies = useCallback((commonCurrencies: string[]): Currency[] => {
    return state.currencies.filter(currency => 
      commonCurrencies.includes(currency.code)
    );
  }, [state.currencies]);

  /**
   * Get currency by code
   */
  const getCurrencyByCode = useCallback((code: string): Currency | undefined => {
    return state.currencies.find(currency => currency.code === code);
  }, [state.currencies]);

  // Load currencies on mount
  useEffect(() => {
    let isCancelled = false;

    const loadInitialCurrencies = async () => {
      // Check cache first
      const cached = loadFromCache();
      if (cached && !isCancelled) {
        dispatch({ type: 'SET_FROM_CACHE', payload: cached });
        return;
      }

      if (isCancelled) return;
      dispatch({ type: 'SET_LOADING', payload: true });

      try {
        const currencies = await currencyService.getActiveCurrencies();
        const now = Date.now();

        if (!isCancelled) {
          dispatch({
            type: 'SET_CURRENCIES',
            payload: { currencies, lastFetched: now }
          });

          const newState: SupportedCurrenciesState = {
            currencies,
            currencyCodes: currencies.map(c => c.code),
            isLoading: false,
            error: null,
            lastFetched: now,
          };
          saveToCache(newState);
        }
      } catch (error) {
        if (!isCancelled) {
          const errorMessage = error instanceof Error ? error.message : "Failed to fetch currencies";
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
        }
      }
    };

    loadInitialCurrencies();

    return () => {
      isCancelled = true;
    };
  }, [currencyService, loadFromCache, saveToCache]);

  return {
    currencies: state.currencies,
    currencyCodes: state.currencyCodes,
    isLoading: state.isLoading,
    error: state.error,
    lastFetched: state.lastFetched,
    fetchCurrencies,
    isCurrencySupported,
    getFilteredCurrencies,
    getCurrencyByCode,
    refreshCurrencies: () => fetchCurrencies(true),
  };
}

/**
 * Hook for getting only common supported currencies
 */
export function useCommonSupportedCurrencies() {
  const { currencies, isLoading, error, getFilteredCurrencies } = useSupportedCurrencies();

  // Common currencies in order of preference
  const commonCurrencyCodes = [
    "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "CNY", "SEK", "NOK",
    "DKK", "PLN", "CZK", "HUF", "RON", "BGN", "HKD", "SGD", "KRW", "TWD",
    "MYR", "THB", "IDR", "PHP", "VN", "INR", "MXN", "BRL", "ARS", "CLP",
    "COP", "PEN", "AED", "SAR", "ILS", "TRY", "ZAR", "EGP", "NZD"
  ];

  const commonCurrencies = getFilteredCurrencies(commonCurrencyCodes);

  return {
    currencies: commonCurrencies,
    isLoading,
    error,
  };
}

/**
 * Simple hook for checking if currencies are supported
 */
export function useCurrencyValidator() {
  const { currencyCodes, isLoading } = useSupportedCurrencies();

  const validateCurrency = useCallback((currencyCode: string): boolean => {
    if (isLoading) return true; // Allow during loading
    return currencyCodes.includes(currencyCode);
  }, [currencyCodes, isLoading]);

  const validateCurrencies = useCallback((currencyCodes: string[]): string[] => {
    if (isLoading) return currencyCodes; // Allow during loading
    return currencyCodes.filter(code => validateCurrency(code));
  }, [validateCurrency, isLoading]);

  return {
    validateCurrency,
    validateCurrencies,
    isLoading,
  };
}

/**
 * Hook that provides currency info with support validation
 */
export function useCurrencyInfo(currencyCode?: string) {
  const { getCurrencyByCode, isCurrencySupported, isLoading } = useSupportedCurrencies();

  const currencyInfo = currencyCode ? getCurrencyByCode(currencyCode) : undefined;
  const isSupported = currencyCode ? isCurrencySupported(currencyCode) : false;

  return {
    currency: currencyInfo,
    isSupported,
    isLoading,
  };
}
